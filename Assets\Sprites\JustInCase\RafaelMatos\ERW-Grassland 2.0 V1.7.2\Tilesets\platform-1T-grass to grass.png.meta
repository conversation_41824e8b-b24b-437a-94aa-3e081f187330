fileFormatVersion: 2
guid: b18274aaeb7908543a585c96a179a676
AssetOrigin:
  serializedVersion: 1
  productId: 272289
  packageName: Epic RPG World Collection
  packageVersion: 1.4
  assetPath: Assets/RafaelMatos/ERW-Grassland 2.0 V1.7.2/Tilesets/platform-1T-grass
    to grass.png
  uploadId: 775377
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 0
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: iOS
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WindowsStoreApps
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: platform-1T-grass to grass_0
      rect:
        serializedVersion: 2
        x: 0
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a313424aa3acc184983629113b6cec3f
      internalID: 446693406
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_1
      rect:
        serializedVersion: 2
        x: 32
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b6be4377e6c39064d9d0b08cb61db40e
      internalID: 2138786107
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_2
      rect:
        serializedVersion: 2
        x: 64
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b9884b25b1ce2a743aa822e5b0c37a38
      internalID: -1619684256
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_3
      rect:
        serializedVersion: 2
        x: 96
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d7a1dee7a6e60c94c9e610fa872068ed
      internalID: -1142320265
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_4
      rect:
        serializedVersion: 2
        x: 128
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1a78a35768904e74a9515c91001d9de8
      internalID: -381535951
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_5
      rect:
        serializedVersion: 2
        x: 160
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d3a9361229702ce45998ccbbf55b2126
      internalID: 116296333
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_6
      rect:
        serializedVersion: 2
        x: 192
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3deab2232deda26438cf202d16d21d06
      internalID: 1828754523
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_7
      rect:
        serializedVersion: 2
        x: 224
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1544c8ffe5781f347ad9ef10b8aa62bb
      internalID: -1406464949
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_8
      rect:
        serializedVersion: 2
        x: 256
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 746b668208aa1184e974ff71c50416db
      internalID: -611479535
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_9
      rect:
        serializedVersion: 2
        x: 288
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 437602f704dcff84bbccb93f39770ce7
      internalID: -1442219920
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_10
      rect:
        serializedVersion: 2
        x: 320
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 01a172acbeaa5af44853e925dc68f26f
      internalID: -1009164814
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_11
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b91b8799ab1847b4da112058d82cecf1
      internalID: 1207373201
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_12
      rect:
        serializedVersion: 2
        x: 32
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0a94d9ce65b05bf48b38dd1b793c0eb4
      internalID: -1215814631
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_13
      rect:
        serializedVersion: 2
        x: 64
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b4992d50a83b34940bbf67bcef8bd61c
      internalID: 767838979
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_14
      rect:
        serializedVersion: 2
        x: 96
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c5cedac892ae09c43a5f9c0cb2c8d451
      internalID: -1033965231
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_15
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2a3880b4b4dd0cb499048fd73b9ae572
      internalID: 190730727
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_16
      rect:
        serializedVersion: 2
        x: 160
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 219c722a1ec716b449edb5387a6b87f8
      internalID: 470340088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_17
      rect:
        serializedVersion: 2
        x: 192
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0d2208be4058af84f8ee0c144e2c8097
      internalID: 1678174187
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_18
      rect:
        serializedVersion: 2
        x: 224
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dd76502a010944b478f629c74968876d
      internalID: -1067800322
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_19
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6325e1298c69104439c73a6d3af1ab4e
      internalID: -987844182
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_20
      rect:
        serializedVersion: 2
        x: 288
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1eb7a23cc3b7cda4b969c66b4fc580f5
      internalID: -525564614
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_21
      rect:
        serializedVersion: 2
        x: 320
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b545185069aaa0d49b13af9573c420c8
      internalID: -626247061
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_22
      rect:
        serializedVersion: 2
        x: 0
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2609b08c0c8993846b7b19858e08df41
      internalID: 651110556
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_23
      rect:
        serializedVersion: 2
        x: 32
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 08e806e8777eecb4e8a0f9dd66c193ec
      internalID: -472117721
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_24
      rect:
        serializedVersion: 2
        x: 64
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 247e2c876ce25f448b24277082ebdf1a
      internalID: -1943355796
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_25
      rect:
        serializedVersion: 2
        x: 96
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a6ee49110f3c0404aafec94b2fc08e06
      internalID: 985103666
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_26
      rect:
        serializedVersion: 2
        x: 128
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6d7f38d2d62703f43a0c3026060c416d
      internalID: 1292576196
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_27
      rect:
        serializedVersion: 2
        x: 160
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 11d076b72c5077d4387b9a0ee9911dcb
      internalID: 924464718
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_28
      rect:
        serializedVersion: 2
        x: 192
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: da65af09b64769243b2106988ccd92d0
      internalID: -265205443
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_29
      rect:
        serializedVersion: 2
        x: 224
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 90adeaf079ef1de42aa2a83efe188740
      internalID: -1391534185
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_30
      rect:
        serializedVersion: 2
        x: 256
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5039adc8fbe6bbb4bac11cb8c4f63987
      internalID: -1202655323
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_31
      rect:
        serializedVersion: 2
        x: 288
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b62a638f376e5d94b8412153b3b7f91c
      internalID: -1885437312
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_32
      rect:
        serializedVersion: 2
        x: 320
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7b3fec01f5ee7b94a98476a9eae876db
      internalID: 1693089064
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_33
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 286887f8e4765094087c8253d86d16a1
      internalID: 1617382137
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_34
      rect:
        serializedVersion: 2
        x: 32
        y: 192
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7dd09eceaa43bdd49ad9860d9cd84ed9
      internalID: -2035936163
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_35
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 91f01f91b2d73424981df473c1edd763
      internalID: 240030863
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_36
      rect:
        serializedVersion: 2
        x: 96
        y: 192
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 155a7d17b115ee44fa71e81c860a60b6
      internalID: 1987442437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_37
      rect:
        serializedVersion: 2
        x: 128
        y: 192
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1ff983d9d76f5a14ca44c15ef694b5e1
      internalID: 1200602899
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_38
      rect:
        serializedVersion: 2
        x: 32
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b8e42c53e1884f549854959dc4d1e61e
      internalID: 14666504
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_39
      rect:
        serializedVersion: 2
        x: 64
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 404087cfd52d6054db21f86fcd32af81
      internalID: 275225236
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_40
      rect:
        serializedVersion: 2
        x: 96
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 32db932fae23f40449e3684660178685
      internalID: 868058303
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_41
      rect:
        serializedVersion: 2
        x: 160
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 98fae206d30484346bb2e4f358296c6a
      internalID: 1782765827
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_42
      rect:
        serializedVersion: 2
        x: 192
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e2b54ba7322c1fd4e83e1def24e8caab
      internalID: 439280433
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_43
      rect:
        serializedVersion: 2
        x: 224
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a590bc47255b7514c997d6d4f724f4c8
      internalID: -415131693
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_44
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7332dfd3d119dad46a4838c35e2ddaff
      internalID: 1812361341
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_45
      rect:
        serializedVersion: 2
        x: 32
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 799d69dd33ff84543a8d693cb65449b2
      internalID: -461538252
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_46
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c391c8f68def78941969c6a72ef85e9e
      internalID: 497644083
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_47
      rect:
        serializedVersion: 2
        x: 96
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 225f82117575570429217ab1618465c0
      internalID: 389845193
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_48
      rect:
        serializedVersion: 2
        x: 160
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b07919455f760b841b10e10dcfc212b3
      internalID: -822679717
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_49
      rect:
        serializedVersion: 2
        x: 192
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0e1dc880089fd2a48bd44f16295b8f8c
      internalID: 2016352874
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_50
      rect:
        serializedVersion: 2
        x: 224
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ae4a47d14f444984d88f1a827ba5ad32
      internalID: -982366912
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_51
      rect:
        serializedVersion: 2
        x: 0
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 876bc9013e7f71243b64e6cecb2b2c79
      internalID: 1853799840
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_52
      rect:
        serializedVersion: 2
        x: 32
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eb8db6dd476e9204f92f841c83f591fc
      internalID: -1465030135
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_53
      rect:
        serializedVersion: 2
        x: 64
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f97bbe49be66ca04a9c260731cd83ff7
      internalID: 469074779
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_54
      rect:
        serializedVersion: 2
        x: 96
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 04ab2f493c79da6458067d025fafdb3f
      internalID: 1442907855
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_55
      rect:
        serializedVersion: 2
        x: 160
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c515dc896cfd4d74591eb1a8f9f7219f
      internalID: 1817029524
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_56
      rect:
        serializedVersion: 2
        x: 192
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 883c9d30572ab4c46a3505f157505bc1
      internalID: -1357651034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_57
      rect:
        serializedVersion: 2
        x: 224
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 99b46a84411ec00489157639cb924d06
      internalID: 1942809469
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_58
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 16b018751b49e9b4e847a8f71246c0e6
      internalID: -178615013
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_59
      rect:
        serializedVersion: 2
        x: 32
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 768121cabd9dff74b8d5eed1457ea3f2
      internalID: 2039920571
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_60
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 078d062441a5e684997916188640c3c3
      internalID: 922873729
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_61
      rect:
        serializedVersion: 2
        x: 96
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7388415ee4a83dd46a96dbf11101f6bd
      internalID: -2147250518
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_62
      rect:
        serializedVersion: 2
        x: 160
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e2f080b1ab791474a818cafcbf13eda8
      internalID: 841007109
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_63
      rect:
        serializedVersion: 2
        x: 224
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 87efbff116d57ab47a6f3812a6db7267
      internalID: 141410416
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_64
      rect:
        serializedVersion: 2
        x: 256
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fdbe6d4075622fd4a9996f040fc3fd68
      internalID: -2113914202
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_65
      rect:
        serializedVersion: 2
        x: 288
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d016bd638579b9c4588f7cb0bb96fe55
      internalID: 266938339
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_66
      rect:
        serializedVersion: 2
        x: 64
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ccb26a0acf0de4645834e1aa4b03b58a
      internalID: -399142059
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_67
      rect:
        serializedVersion: 2
        x: 96
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 07b7963e1d355814c88469a9575edb60
      internalID: -1339421544
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_68
      rect:
        serializedVersion: 2
        x: 160
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3614b5f0e0a084846ade089bf2bd93c0
      internalID: -1511802088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_69
      rect:
        serializedVersion: 2
        x: 224
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 047548826579dfa48ad4e06756e6d085
      internalID: -1569287589
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_70
      rect:
        serializedVersion: 2
        x: 256
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0866ec8b3e3824c4cac53c3f059b9d5d
      internalID: -72883201
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: platform-1T-grass to grass_71
      rect:
        serializedVersion: 2
        x: 288
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 35fa7d94c0e044a4c8cc5f5de0a1f21b
      internalID: -1341457831
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      platform-1T-grass to grass_0: 446693406
      platform-1T-grass to grass_1: 2138786107
      platform-1T-grass to grass_10: -1009164814
      platform-1T-grass to grass_11: 1207373201
      platform-1T-grass to grass_12: -1215814631
      platform-1T-grass to grass_13: 767838979
      platform-1T-grass to grass_14: -1033965231
      platform-1T-grass to grass_15: 190730727
      platform-1T-grass to grass_16: 470340088
      platform-1T-grass to grass_17: 1678174187
      platform-1T-grass to grass_18: -1067800322
      platform-1T-grass to grass_19: -987844182
      platform-1T-grass to grass_2: -1619684256
      platform-1T-grass to grass_20: -525564614
      platform-1T-grass to grass_21: -626247061
      platform-1T-grass to grass_22: 651110556
      platform-1T-grass to grass_23: -472117721
      platform-1T-grass to grass_24: -1943355796
      platform-1T-grass to grass_25: 985103666
      platform-1T-grass to grass_26: 1292576196
      platform-1T-grass to grass_27: 924464718
      platform-1T-grass to grass_28: -265205443
      platform-1T-grass to grass_29: -1391534185
      platform-1T-grass to grass_3: -1142320265
      platform-1T-grass to grass_30: -1202655323
      platform-1T-grass to grass_31: -1885437312
      platform-1T-grass to grass_32: 1693089064
      platform-1T-grass to grass_33: 1617382137
      platform-1T-grass to grass_34: -2035936163
      platform-1T-grass to grass_35: 240030863
      platform-1T-grass to grass_36: 1987442437
      platform-1T-grass to grass_37: 1200602899
      platform-1T-grass to grass_38: 14666504
      platform-1T-grass to grass_39: 275225236
      platform-1T-grass to grass_4: -381535951
      platform-1T-grass to grass_40: 868058303
      platform-1T-grass to grass_41: 1782765827
      platform-1T-grass to grass_42: 439280433
      platform-1T-grass to grass_43: -415131693
      platform-1T-grass to grass_44: 1812361341
      platform-1T-grass to grass_45: -461538252
      platform-1T-grass to grass_46: 497644083
      platform-1T-grass to grass_47: 389845193
      platform-1T-grass to grass_48: -822679717
      platform-1T-grass to grass_49: 2016352874
      platform-1T-grass to grass_5: 116296333
      platform-1T-grass to grass_50: -982366912
      platform-1T-grass to grass_51: 1853799840
      platform-1T-grass to grass_52: -1465030135
      platform-1T-grass to grass_53: 469074779
      platform-1T-grass to grass_54: 1442907855
      platform-1T-grass to grass_55: 1817029524
      platform-1T-grass to grass_56: -1357651034
      platform-1T-grass to grass_57: 1942809469
      platform-1T-grass to grass_58: -178615013
      platform-1T-grass to grass_59: 2039920571
      platform-1T-grass to grass_6: 1828754523
      platform-1T-grass to grass_60: 922873729
      platform-1T-grass to grass_61: -2147250518
      platform-1T-grass to grass_62: 841007109
      platform-1T-grass to grass_63: 141410416
      platform-1T-grass to grass_64: -2113914202
      platform-1T-grass to grass_65: 266938339
      platform-1T-grass to grass_66: -399142059
      platform-1T-grass to grass_67: -1339421544
      platform-1T-grass to grass_68: -1511802088
      platform-1T-grass to grass_69: -1569287589
      platform-1T-grass to grass_7: -1406464949
      platform-1T-grass to grass_70: -72883201
      platform-1T-grass to grass_71: -1341457831
      platform-1T-grass to grass_8: -611479535
      platform-1T-grass to grass_9: -1442219920
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
