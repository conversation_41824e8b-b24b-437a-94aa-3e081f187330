fileFormatVersion: 2
guid: 20f9fbc4c1c19ab4fac12abfa46cf972
AssetOrigin:
  serializedVersion: 1
  productId: 272289
  packageName: Epic RPG World Collection
  packageVersion: 1.4
  assetPath: Assets/RafaelMatos/ERW-Grassland 2.0 V1.7.2/Tilesets/stone path to grass.png
  uploadId: 775377
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 0
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: iOS
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WindowsStoreApps
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: stone path to grass_0
      rect:
        serializedVersion: 2
        x: 0
        y: 352
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 24243d2bb35aefc4db5b85d8e94142b9
      internalID: -600651762
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_1
      rect:
        serializedVersion: 2
        x: 32
        y: 352
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5575b63d2b8e13e458cd04b872cd10ae
      internalID: -900669891
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_2
      rect:
        serializedVersion: 2
        x: 64
        y: 352
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a4a1c7509a4bbd94199f293471a92fe6
      internalID: -440343183
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_3
      rect:
        serializedVersion: 2
        x: 96
        y: 352
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 287dcfc9cc9e3f04294b770b3749ec1f
      internalID: 196651567
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_4
      rect:
        serializedVersion: 2
        x: 128
        y: 352
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 12c5198bc4a631d4eb1deafa69eee069
      internalID: -244184159
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_5
      rect:
        serializedVersion: 2
        x: 0
        y: 320
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 707bd476900831e4fa7a23393dd9871e
      internalID: 1003755794
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_6
      rect:
        serializedVersion: 2
        x: 32
        y: 320
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0832f4db7dd9eb54c8a9a63ed28c09e1
      internalID: 344699798
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_7
      rect:
        serializedVersion: 2
        x: 64
        y: 320
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: afc0efd6e8661bd4d8a5bf3c7e757fb6
      internalID: 1749948394
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_8
      rect:
        serializedVersion: 2
        x: 96
        y: 320
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7ae26fc1b4f2c1d43aacb7dde819e444
      internalID: -1809806279
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_9
      rect:
        serializedVersion: 2
        x: 128
        y: 320
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1f55825c20de9e84883fff16d7b120f5
      internalID: 888041090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_10
      rect:
        serializedVersion: 2
        x: 0
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 36e3e1eae311b614887722ae3d7bb4f0
      internalID: -1432918054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_11
      rect:
        serializedVersion: 2
        x: 32
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eef6b83f7a2f0894193c2afd2c1f8d46
      internalID: -1701223106
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_12
      rect:
        serializedVersion: 2
        x: 64
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 36da3343070ca1a44b134a9c1e46dcc6
      internalID: -1072827182
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_13
      rect:
        serializedVersion: 2
        x: 96
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: deecc087878ffc94083b9220d55d7c8a
      internalID: -1011563836
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_14
      rect:
        serializedVersion: 2
        x: 128
        y: 288
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b874bcc80ae9bd54ebd75b2262894c59
      internalID: -256119521
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_15
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f2785c8bf55ad2a478ecd1e7a59da8c7
      internalID: 1912773973
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_16
      rect:
        serializedVersion: 2
        x: 32
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 34ed294c12303fa4c8558e9bc01e39aa
      internalID: -5204842
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_17
      rect:
        serializedVersion: 2
        x: 64
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 37ba23028b473e641ae0b6f93f6a8875
      internalID: -650573079
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_18
      rect:
        serializedVersion: 2
        x: 96
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8bbb0283be2502d4dae32a9db3c2f970
      internalID: 1996924617
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_19
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bd2b412b3489ceb4b8edc4a5b38f58a1
      internalID: -2034920768
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_20
      rect:
        serializedVersion: 2
        x: 32
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0cbda65adf30d0845ad5e5e218a3c1eb
      internalID: -656525427
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_21
      rect:
        serializedVersion: 2
        x: 64
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2265e2794e7622a40b78518af4d67d71
      internalID: -100984143
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_22
      rect:
        serializedVersion: 2
        x: 96
        y: 224
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7c23837eae419e849beaba6f7de3e6ff
      internalID: -859700893
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_23
      rect:
        serializedVersion: 2
        x: 32
        y: 192
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c9f50801206b2db43b35d19a612d3c08
      internalID: 637801975
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_24
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 63a31cad560bd364fb8623cf2685852c
      internalID: -1153092158
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_25
      rect:
        serializedVersion: 2
        x: 128
        y: 192
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c1dd991b9f2340d48a7ab1d6ee238046
      internalID: 2089101835
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_26
      rect:
        serializedVersion: 2
        x: 160
        y: 192
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4c0b93961aedeee4393ec0e626821ab1
      internalID: -80063176
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_27
      rect:
        serializedVersion: 2
        x: 0
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b8869b3475869a24484877de404d0d15
      internalID: 108539208
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_28
      rect:
        serializedVersion: 2
        x: 32
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 84eda8a9a060f3e47809781db1b1db65
      internalID: 1774789634
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_29
      rect:
        serializedVersion: 2
        x: 64
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 14f88f997f0d57a4b9fb3bce9ba90d7c
      internalID: -1716135060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_30
      rect:
        serializedVersion: 2
        x: 96
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 648d2461b72e7f241a5983c705fc6644
      internalID: -371300544
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_31
      rect:
        serializedVersion: 2
        x: 128
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b3e4f514a34b4eb4fa45edc00649a2a5
      internalID: -836005450
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_32
      rect:
        serializedVersion: 2
        x: 160
        y: 160
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d267a5d555202f3488d537d3008b1f8c
      internalID: -956687852
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_33
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 339308b7c42de88438ac0e07d9b09c91
      internalID: -1412094607
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_34
      rect:
        serializedVersion: 2
        x: 32
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9dc431e739bfec741a4b5f683fc9ee3d
      internalID: -1770057280
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_35
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d61ac756563a953438a50945978597b0
      internalID: -1204273910
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_36
      rect:
        serializedVersion: 2
        x: 96
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b3cd161f8730fb14eb2ac5fc480908bd
      internalID: -1244555227
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_37
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ea6082b5e0559fc45819fd62fbaa146b
      internalID: -1683130266
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_38
      rect:
        serializedVersion: 2
        x: 160
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 918b2d38e276efd4d8619bcf01e8df7f
      internalID: 1931237226
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_39
      rect:
        serializedVersion: 2
        x: 32
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 448a19f60fc183441a9e93571ca4a1c6
      internalID: -1117395376
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_40
      rect:
        serializedVersion: 2
        x: 64
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5852274ab26649249a8b00c37f4bf3ce
      internalID: -707062984
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_41
      rect:
        serializedVersion: 2
        x: 128
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1ab8ba1040c4e82489daa4cf16882e55
      internalID: -792179256
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_42
      rect:
        serializedVersion: 2
        x: 160
        y: 96
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ca44592ec21c90d4184cd08e9e720f30
      internalID: 1338181108
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_43
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8020e4a7d6dad3d4dbb7e88860ec6c2c
      internalID: -1313372342
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_44
      rect:
        serializedVersion: 2
        x: 32
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ab8d7754b2829e447aaefc4b8908f193
      internalID: -1108915294
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_45
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8dfd52537248b1743837d64eb4c49326
      internalID: -495452837
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_46
      rect:
        serializedVersion: 2
        x: 96
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6d35de23771cf324197b0c735d60e336
      internalID: 1207425121
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_47
      rect:
        serializedVersion: 2
        x: 128
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b7cda811a79be5b40a945028ca271cb0
      internalID: -778988927
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_48
      rect:
        serializedVersion: 2
        x: 160
        y: 64
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 319084c116cff1b4ca81609dfff9ce0d
      internalID: 744369141
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_49
      rect:
        serializedVersion: 2
        x: 0
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e4fa600f622e0354590e5fdc162ba24a
      internalID: 598970528
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_50
      rect:
        serializedVersion: 2
        x: 32
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0e6ed2d681202cc4da6789841a16c1c9
      internalID: 1734289936
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_51
      rect:
        serializedVersion: 2
        x: 64
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b2b78b6757e871547a26aabd9355db92
      internalID: -711461240
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_52
      rect:
        serializedVersion: 2
        x: 96
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2e3733151e770b54094e54677fea202a
      internalID: -1416087052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_53
      rect:
        serializedVersion: 2
        x: 128
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5af030e3e1ffa6b448300f79e06bd029
      internalID: 659737605
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_54
      rect:
        serializedVersion: 2
        x: 160
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e07387cd0dcbdac409e49b96c2316894
      internalID: 370133386
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_55
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e631bfb8b697d7d4198145ba81a09346
      internalID: -877304784
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_56
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 952a50154569ff2478325249cb2ddd3b
      internalID: 7212782
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_57
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e13794326396f624e9429e29506fb682
      internalID: 42943635
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_58
      rect:
        serializedVersion: 2
        x: 96
        y: 0
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 30092ebc973566940bf0a207eca38d31
      internalID: 1891087280
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: stone path to grass_59
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c8109b66f90dcae4eb80f1e848ba75cf
      internalID: -1104883263
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      stone path to grass_0: -600651762
      stone path to grass_1: -900669891
      stone path to grass_10: -1432918054
      stone path to grass_11: -1701223106
      stone path to grass_12: -1072827182
      stone path to grass_13: -1011563836
      stone path to grass_14: -256119521
      stone path to grass_15: 1912773973
      stone path to grass_16: -5204842
      stone path to grass_17: -650573079
      stone path to grass_18: 1996924617
      stone path to grass_19: -2034920768
      stone path to grass_2: -440343183
      stone path to grass_20: -656525427
      stone path to grass_21: -100984143
      stone path to grass_22: -859700893
      stone path to grass_23: 637801975
      stone path to grass_24: -1153092158
      stone path to grass_25: 2089101835
      stone path to grass_26: -80063176
      stone path to grass_27: 108539208
      stone path to grass_28: 1774789634
      stone path to grass_29: -1716135060
      stone path to grass_3: 196651567
      stone path to grass_30: -371300544
      stone path to grass_31: -836005450
      stone path to grass_32: -956687852
      stone path to grass_33: -1412094607
      stone path to grass_34: -1770057280
      stone path to grass_35: -1204273910
      stone path to grass_36: -1244555227
      stone path to grass_37: -1683130266
      stone path to grass_38: 1931237226
      stone path to grass_39: -1117395376
      stone path to grass_4: -244184159
      stone path to grass_40: -707062984
      stone path to grass_41: -792179256
      stone path to grass_42: 1338181108
      stone path to grass_43: -1313372342
      stone path to grass_44: -1108915294
      stone path to grass_45: -495452837
      stone path to grass_46: 1207425121
      stone path to grass_47: -778988927
      stone path to grass_48: 744369141
      stone path to grass_49: 598970528
      stone path to grass_5: 1003755794
      stone path to grass_50: 1734289936
      stone path to grass_51: -711461240
      stone path to grass_52: -1416087052
      stone path to grass_53: 659737605
      stone path to grass_54: 370133386
      stone path to grass_55: -877304784
      stone path to grass_56: 7212782
      stone path to grass_57: 42943635
      stone path to grass_58: 1891087280
      stone path to grass_59: -1104883263
      stone path to grass_6: 344699798
      stone path to grass_7: 1749948394
      stone path to grass_8: -1809806279
      stone path to grass_9: 888041090
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
